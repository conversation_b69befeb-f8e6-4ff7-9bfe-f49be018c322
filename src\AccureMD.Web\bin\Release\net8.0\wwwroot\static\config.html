<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccureMD - Configuration</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Teams Design System Variables */
        :root {
            --teams-purple: #6264A7;
            --teams-purple-dark: #464775;
            --teams-gray-900: #252423;
            --teams-gray-800: #323130;
            --teams-gray-600: #605E5C;
            --teams-gray-300: #C8C6C4;
            --teams-gray-100: #F3F2F1;
            --teams-gray-50: #FAF9F8;
            --teams-white: #FFFFFF;
            --teams-blue: #0078D4;
            --teams-font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
            --teams-font-size-200: 14px;
            --teams-font-size-300: 16px;
            --teams-font-size-400: 18px;
            --teams-space-lg: 16px;
            --teams-space-xl: 20px;
            --teams-space-xxl: 24px;
            --teams-radius-lg: 8px;
            --teams-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        @font-face {
            font-family: 'Segoe Fluent Icons';
            src: local('Segoe Fluent Icons');
        }

        .fluent-icon {
            font-family: 'Segoe Fluent Icons', 'Segoe UI Symbol', sans-serif;
            font-style: normal;
            font-weight: normal;
            speak: none;
            display: inline-block;
            text-decoration: inherit;
            width: 1em;
            text-align: center;
            font-variant: normal;
            text-transform: none;
            line-height: 1em;
        }

        body {
            font-family: var(--teams-font-family);
            background-color: var(--teams-gray-50);
            padding: var(--teams-space-xl);
            margin: 0;
            color: var(--teams-gray-900);
            font-size: var(--teams-font-size-200);
            line-height: 1.4;
        }

        .config-container {
            max-width: 600px;
            margin: 0 auto;
            background: var(--teams-white);
            border-radius: var(--teams-radius-lg);
            box-shadow: var(--teams-shadow-md);
            overflow: hidden;
        }

        .header {
            background: var(--teams-purple);
            color: var(--teams-white);
            padding: var(--teams-space-xxl);
            text-align: center;
        }

        .header h2 {
            margin: 0;
            font-weight: 600;
        }

        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
        }

        .config-form {
            padding: 32px;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-section h4 {
            color: #6264a7;
            margin-bottom: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }

        .form-control {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            border-color: #6264a7;
            box-shadow: 0 0 0 0.2rem rgba(98, 100, 167, 0.25);
        }

        .form-check {
            margin-bottom: 12px;
        }

        .form-check-input:checked {
            background-color: #6264a7;
            border-color: #6264a7;
        }

        .btn-primary {
            background: #6264a7;
            border-color: #6264a7;
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-primary:hover {
            background: #5a5c9d;
            border-color: #5a5c9d;
        }

        .btn-secondary {
            background: #6c757d;
            border-color: #6c757d;
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 8px;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .feature-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            transition: border-color 0.2s;
        }

        .feature-card.enabled {
            border-color: #6264a7;
            background-color: #f8f9ff;
        }

        .feature-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .feature-title {
            font-weight: 600;
            color: #333;
        }

        .feature-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .feature-options {
            display: none;
            padding-top: 12px;
            border-top: 1px solid #e0e0e0;
        }

        .feature-options.show {
            display: block;
        }

        .btn-group {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <!-- Header -->
        <div class="header">
            <h2><span class="fluent-icon">&#xE713;</span> AccureMD Configuration</h2>
            <p>Configure your meeting transcription preferences</p>
        </div>

        <!-- Configuration Form -->
        <div class="config-form">
            <div id="alertContainer"></div>

            <!-- Transcription Settings -->
            <div class="form-section">
                <h4><i class="fas fa-microphone"></i> Transcription Settings</h4>
                
                <div class="feature-card" id="transcriptionCard">
                    <div class="feature-header">
                        <div class="feature-title">Real-time Transcription</div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableTranscription" checked>
                        </div>
                    </div>
                    <div class="feature-description">
                        Enable real-time speech-to-text transcription during meetings
                    </div>
                    <div class="feature-options" id="transcriptionOptions">
                        <div class="form-group">
                            <label class="form-label" for="defaultLanguage">Default Language</label>
                            <select class="form-control" id="defaultLanguage">
                                <option value="en-US">English (US)</option>
                                <option value="en-GB">English (UK)</option>
                                <option value="es-ES">Spanish</option>
                                <option value="fr-FR">French</option>
                                <option value="de-DE">German</option>
                                <option value="ja-JP">Japanese</option>
                                <option value="zh-CN">Chinese (Simplified)</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableSpeakerDiarization" checked>
                            <label class="form-check-label" for="enableSpeakerDiarization">
                                Enable Speaker Identification
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enablePunctuation" checked>
                            <label class="form-check-label" for="enablePunctuation">
                                Auto-punctuation
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recording Settings -->
            <div class="form-section">
                <h4><i class="fas fa-video"></i> Recording Settings</h4>
                
                <div class="feature-card" id="recordingCard">
                    <div class="feature-header">
                        <div class="feature-title">Meeting Recording</div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableRecording">
                        </div>
                    </div>
                    <div class="feature-description">
                        Record audio and video during meetings for later review
                    </div>
                    <div class="feature-options" id="recordingOptions">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="recordAudio" checked>
                            <label class="form-check-label" for="recordAudio">
                                Record Audio
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="recordVideo">
                            <label class="form-check-label" for="recordVideo">
                                Record Video
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="recordScreenShare">
                            <label class="form-check-label" for="recordScreenShare">
                                Record Screen Sharing
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Privacy Settings -->
            <div class="form-section">
                <h4><i class="fas fa-shield-alt"></i> Privacy & Permissions</h4>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="autoJoinMeetings">
                    <label class="form-check-label" for="autoJoinMeetings">
                        Automatically join scheduled meetings
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="requestPermission" checked>
                    <label class="form-check-label" for="requestPermission">
                        Always request permission before recording
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="shareTranscripts">
                    <label class="form-check-label" for="shareTranscripts">
                        Share transcripts with meeting participants
                    </label>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="cancelConfiguration()">
                    Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="saveConfiguration()">
                    <i class="fas fa-save"></i> Save Configuration
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Teams SDK
        microsoftTeams.app.initialize().then(() => {
            console.log('Teams SDK initialized');
            loadConfiguration();
        });

        // Load existing configuration
        function loadConfiguration() {
            try {
                // Load saved settings from localStorage or API
                const savedConfig = localStorage.getItem('accuremd-config');
                if (savedConfig) {
                    const config = JSON.parse(savedConfig);
                    applyConfiguration(config);
                }
                
                // Update feature card states
                updateFeatureCards();
                
            } catch (error) {
                console.error('Error loading configuration:', error);
                showAlert('Warning: Could not load saved configuration', 'warning');
            }
        }

        // Apply configuration to form
        function applyConfiguration(config) {
            // Transcription settings
            document.getElementById('enableTranscription').checked = config.enableTranscription !== false;
            document.getElementById('defaultLanguage').value = config.defaultLanguage || 'en-US';
            document.getElementById('enableSpeakerDiarization').checked = config.enableSpeakerDiarization !== false;
            document.getElementById('enablePunctuation').checked = config.enablePunctuation !== false;
            
            // Recording settings
            document.getElementById('enableRecording').checked = config.enableRecording || false;
            document.getElementById('recordAudio').checked = config.recordAudio !== false;
            document.getElementById('recordVideo').checked = config.recordVideo || false;
            document.getElementById('recordScreenShare').checked = config.recordScreenShare || false;
            
            // Privacy settings
            document.getElementById('autoJoinMeetings').checked = config.autoJoinMeetings || false;
            document.getElementById('requestPermission').checked = config.requestPermission !== false;
            document.getElementById('shareTranscripts').checked = config.shareTranscripts || false;
        }

        // Save configuration
        function saveConfiguration() {
            try {
                const config = {
                    enableTranscription: document.getElementById('enableTranscription').checked,
                    defaultLanguage: document.getElementById('defaultLanguage').value,
                    enableSpeakerDiarization: document.getElementById('enableSpeakerDiarization').checked,
                    enablePunctuation: document.getElementById('enablePunctuation').checked,
                    enableRecording: document.getElementById('enableRecording').checked,
                    recordAudio: document.getElementById('recordAudio').checked,
                    recordVideo: document.getElementById('recordVideo').checked,
                    recordScreenShare: document.getElementById('recordScreenShare').checked,
                    autoJoinMeetings: document.getElementById('autoJoinMeetings').checked,
                    requestPermission: document.getElementById('requestPermission').checked,
                    shareTranscripts: document.getElementById('shareTranscripts').checked,
                    savedAt: new Date().toISOString()
                };

                // Save to localStorage
                localStorage.setItem('accuremd-config', JSON.stringify(config));
                
                // Show success message
                showAlert('Configuration saved successfully!', 'success');
                
                // Notify Teams that configuration is complete
                microsoftTeams.pages.config.setConfig({
                    entityId: 'accuremd-transcription',
                    contentUrl: window.location.origin + '/transcription',
                    suggestedDisplayName: 'AccureMD Transcription'
                });
                
                microsoftTeams.pages.config.setValidityState(true);
                
            } catch (error) {
                console.error('Error saving configuration:', error);
                showAlert('Error saving configuration. Please try again.', 'danger');
            }
        }

        // Cancel configuration
        function cancelConfiguration() {
            // Close the configuration dialog
            microsoftTeams.pages.config.setValidityState(false);
        }

        // Update feature card states
        function updateFeatureCards() {
            const transcriptionEnabled = document.getElementById('enableTranscription').checked;
            const recordingEnabled = document.getElementById('enableRecording').checked;
            
            updateFeatureCard('transcriptionCard', 'transcriptionOptions', transcriptionEnabled);
            updateFeatureCard('recordingCard', 'recordingOptions', recordingEnabled);
        }

        // Update individual feature card
        function updateFeatureCard(cardId, optionsId, enabled) {
            const card = document.getElementById(cardId);
            const options = document.getElementById(optionsId);
            
            if (enabled) {
                card.classList.add('enabled');
                options.classList.add('show');
            } else {
                card.classList.remove('enabled');
                options.classList.remove('show');
            }
        }

        // Show alert message
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 3000);
            }
        }

        // Event listeners
        document.getElementById('enableTranscription').addEventListener('change', updateFeatureCards);
        document.getElementById('enableRecording').addEventListener('change', updateFeatureCards);

        // Initialize feature cards
        updateFeatureCards();
    </script>
</body>
</html>

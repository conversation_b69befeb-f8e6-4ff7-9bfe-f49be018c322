{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AccureMD.Core/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Graph": "5.42.0", "Microsoft.Graph.Auth": "1.0.0-preview.7", "Microsoft.IdentityModel.Tokens": "7.3.1", "System.ComponentModel.Annotations": "5.0.0", "System.IdentityModel.Tokens.Jwt": "7.3.1", "System.Text.Json": "8.0.5"}, "runtime": {"AccureMD.Core.dll": {}}}, "Azure.Core/1.37.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.37.0.0", "fileVersion": "1.3700.24.6103"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Graph/5.42.0": {"dependencies": {"Microsoft.Graph.Core": "3.1.7"}, "runtime": {"lib/net5.0/Microsoft.Graph.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Graph.Auth/1.0.0-preview.7": {"dependencies": {"Microsoft.Graph.Core": "3.1.7", "Microsoft.Identity.Client": "4.29.0", "NETStandard.Library": "2.0.3", "System.Security.Claims": "4.3.0", "System.Security.Principal": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Graph.Auth.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Graph.Core/3.1.7": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.3.1", "Microsoft.Kiota.Abstractions": "1.7.9", "Microsoft.Kiota.Authentication.Azure": "1.1.3", "Microsoft.Kiota.Http.HttpClientLibrary": "1.3.6", "Microsoft.Kiota.Serialization.Form": "1.1.3", "Microsoft.Kiota.Serialization.Json": "1.1.5", "Microsoft.Kiota.Serialization.Multipart": "1.1.2", "Microsoft.Kiota.Serialization.Text": "1.1.2", "NETStandard.Library": "2.0.3", "System.Security.Claims": "4.3.0"}, "runtime": {"lib/net6.0/Microsoft.Graph.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.29.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/7.3.1": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Logging/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.3.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Protocols/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.3.1", "Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.3.1", "System.IdentityModel.Tokens.Jwt": "7.3.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Tokens/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.3.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "Microsoft.Kiota.Abstractions/1.7.9": {"dependencies": {"Std.UriTemplate": "0.0.50", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "1.7.9.0", "fileVersion": "1.7.9.0"}}}, "Microsoft.Kiota.Authentication.Azure/1.1.3": {"dependencies": {"Azure.Core": "1.37.0", "Microsoft.Kiota.Abstractions": "1.7.9", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Authentication.Azure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.3.6": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.7.9", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "1.3.6.0", "fileVersion": "1.3.6.0"}}}, "Microsoft.Kiota.Serialization.Form/1.1.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.7.9"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Json/1.1.5": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.7.9", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Multipart/1.1.2": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.7.9"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Text/1.1.2": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.7.9"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Std.UriTemplate/0.0.50": {"runtime": {"lib/net5.0/Std.UriTemplate.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.3.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.3.1", "Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.1.50203"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/8.0.5": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"AccureMD.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZSWV/ftBM/c/+Eo2hlzeEWntjqNG+8TWXX/DAKjBSWIf7nEvFILQoJL7JZx5HjypDvdNUMj5J2ji8ZpFFSghSg==", "path": "azure.core/1.37.0", "hashPath": "azure.core.1.37.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Graph/5.42.0": {"type": "package", "serviceable": true, "sha512": "sha512-uWB6kRkVKwAq2BS8Pr/UVfTKh0g7GqV9m3NnIQn7Sr+fOp4NTGy6lzri42+Ur1kEeWWS7o3WaRZI3BX6D4tkxg==", "path": "microsoft.graph/5.42.0", "hashPath": "microsoft.graph.5.42.0.nupkg.sha512"}, "Microsoft.Graph.Auth/1.0.0-preview.7": {"type": "package", "serviceable": true, "sha512": "sha512-HZlh6hS01KUT0KpWdf7UDQKuWn2kpoBNps+wfolhsNwat3vaeXtOttFytvCzEdYm6+7BK0KxZCDXqRBe1y+2zw==", "path": "microsoft.graph.auth/1.0.0-preview.7", "hashPath": "microsoft.graph.auth.1.0.0-preview.7.nupkg.sha512"}, "Microsoft.Graph.Core/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-5Xl0nQMOeRQxApoy3iZSc79Pj1OZ++n9ItPAFinJZk9twrqBfSvkXtVzA4dwS4FBq8EeH2TAtUvxU8EzTEcMvQ==", "path": "microsoft.graph.core/3.1.7", "hashPath": "microsoft.graph.core.3.1.7.nupkg.sha512"}, "Microsoft.Identity.Client/4.29.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pr0zGK5UVOH1wNIMRVtakImDX0OWmI87HANvOQqnhahkeevVOZBnsw1QouEb5eZlLvMciuSaShuSz0806bmCvQ==", "path": "microsoft.identity.client/4.29.0", "hashPath": "microsoft.identity.client.4.29.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-gIw8Sr5ZpuzKFBTfJonh2F54DivTzm5IIK15QB4Y6uE30uQdEO1NnCojTC/b6sWZoZzD0sdBa6SqwMXhucD+nA==", "path": "microsoft.identitymodel.abstractions/7.3.1", "hashPath": "microsoft.identitymodel.abstractions.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-mXA6AoaD5uZqtsKghgRiupBhyXNii8p9F2BjNLnDGud0tZLS5+4Fio2YAGjFXhnkc80CqgQ61X5U1gUNnDEoKQ==", "path": "microsoft.identitymodel.jsonwebtokens/7.3.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-uPt2aiRUCbcOc0Wk+dDCSClFfPNs3S3Z7fmy50MoxJ1mGmtVUDMpyRJeYzZ/16x4rL19T+g2zrzjcWoitp5+gQ==", "path": "microsoft.identitymodel.logging/7.3.1", "hashPath": "microsoft.identitymodel.logging.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-BA+048lSJsWecQDaRYoEAtvSxb9LKv54eku1jiWHc5Giu0FG5nNgm/pKiBheOJSmXw2ZPl048HyqotDzDU6jXg==", "path": "microsoft.identitymodel.protocols/7.3.1", "hashPath": "microsoft.identitymodel.protocols.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-TK3XOKE+7/BXciZyn71K8Csyr4zYbyo0EA/G5AUSR8wfrh3NBCwhytJeKq8QN41bJV++8xAmqesR9g/paoQ2zg==", "path": "microsoft.identitymodel.protocols.openidconnect/7.3.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-/c/p8/3CAH706c0ii5uTgSb/8M/jwyuurtdMeKTBeKFU9aA+EZrLu1M8aaS3CSlGaxoxsoaxr4/+KXykgQ4VgQ==", "path": "microsoft.identitymodel.tokens/7.3.1", "hashPath": "microsoft.identitymodel.tokens.7.3.1.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.7.9": {"type": "package", "serviceable": true, "sha512": "sha512-GsplV0+pcYox4uKfs2VFL+GzYJnl4XlQZ03GY1uvU3ZJHgAdZ75twis+sMJZXIB9WnBIc0QmXry1gqZOf0iMZg==", "path": "microsoft.kiota.abstractions/1.7.9", "hashPath": "microsoft.kiota.abstractions.1.7.9.nupkg.sha512"}, "Microsoft.Kiota.Authentication.Azure/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-6Sc313i6el2CiJ2zvvt297f/qaogzWzEvw2ZaK4AFP4wBTzXB1aFUEceSUChjFlsGtQTZTq+TOEEUZQfjmnW9Q==", "path": "microsoft.kiota.authentication.azure/1.1.3", "hashPath": "microsoft.kiota.authentication.azure.1.1.3.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.3.6": {"type": "package", "serviceable": true, "sha512": "sha512-VQ1wPHxZFsZen8o3oTvaJ8Rk/BI8sMyvjYnHuKT2QMEt7WkUSCOl0RFdNT46hcef0F8FKPw/AP6QUxLP8kzItw==", "path": "microsoft.kiota.http.httpclientlibrary/1.3.6", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.3.6.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-k/B/klqPuDDxhL/0yMwXr7fbBg7uM24O/R+lZYlz2izwPxfZX7dOApetJaab2P0eFat/kKGVlAnS1EKuz0PJTA==", "path": "microsoft.kiota.serialization.form/1.1.3", "hashPath": "microsoft.kiota.serialization.form.1.1.3.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-Vn4+WbZQNMmhmxz5cOTOPCY1cr+SHphHFTZxvzolsCVMrg9jzNN5yxKJ05XksILyg9JPVNTdaogBWZv/pWl0OA==", "path": "microsoft.kiota.serialization.json/1.1.5", "hashPath": "microsoft.kiota.serialization.json.1.1.5.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-R0wLb9N4iLUOvdWCeg1wt+GeX5J7cJCaiO4t1hI4qVC7kU9s9qsw7KQQcpSUeteg+7T/v/b1NZtCRrj8ZP+9lw==", "path": "microsoft.kiota.serialization.multipart/1.1.2", "hashPath": "microsoft.kiota.serialization.multipart.1.1.2.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-7Nyt3K9oELJtilR46mG1BowdCEc5Ts5ZrMyUE1Mbi2gPXtxNvZrYvU1tLHoU4v3T+ezJiUjwvJEpJ3cGa+3Mfw==", "path": "microsoft.kiota.serialization.text/1.1.2", "hashPath": "microsoft.kiota.serialization.text.1.1.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Std.UriTemplate/0.0.50": {"type": "package", "serviceable": true, "sha512": "sha512-ro7KBMw+5GR+3gyszCl3zUQroX6+I39u9K4Gu12KZNmepOhpLQppKcdcLJY18H3PhX6O05Tgo6rO6lryFW7mew==", "path": "std.uritemplate/0.0.50", "hashPath": "std.uritemplate.0.0.50.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-iE8biOWyAC1NnYcZGcgXErNACvIQ6Gcmg5s28gsjVbyyYdF9NdKsYzAPAsO3KGK86EQjpToI1AO82XbG8chkzA==", "path": "system.identitymodel.tokens.jwt/7.3.1", "hashPath": "system.identitymodel.tokens.jwt.7.3.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}
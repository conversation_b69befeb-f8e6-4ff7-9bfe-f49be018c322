<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccureMD - Meeting Transcription</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    
    <style>
        @font-face {
            font-family: 'Segoe Fluent Icons';
            src: local('Segoe Fluent Icons');
        }
        .fluent-icon {
            font-family: 'Segoe Fluent Icons', 'Segoe UI Symbol', sans-serif;
            font-style: normal; font-weight: normal; speak: none; display: inline-block;
            text-decoration: inherit; width: 1em; text-align: center; font-variant: normal;
            text-transform: none; line-height: 1em; -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>
    
    <style>
        :root {
            /* Core Font */
            --teams-font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
            
            /* Refined Dark Theme Palette */
            --bg-primary: #1A1924;      /* Deep, desaturated purple-blue */
            --bg-secondary: #242230;    /* Slightly lighter for UI elements */
            --bg-tertiary: #312F40;      /* Hover states, inputs */
            --text-primary: #EAEAEB;    /* High-contrast off-white for readability */
            --text-secondary: #A09DAA;  /* Dimmer text for status, icons */
            --accent-purple: #A995E0;   /* Softer, premium purple */
            --accent-green: #37A276;    /* Success, start button */
            --accent-red: #D13438;      /* Error, stop button */
            --accent-blue: #3E92CC;     /* Export, focus rings */
            --border-glow: rgba(255, 255, 255, 0.05); /* Subtle glow effect */
            
            --radius-sm: 4px;
            --radius-md: 8px;
        }

        /* Wrapper to simulate sidebar view */
        body {
            font-family: var(--teams-font-family);
            background-color: #0d0d0d;
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: grid;
            place-items: center;
            box-sizing: border-box;
        }

        .sidebar-container {
            width: 320px;
            min-width: 320px;
            max-width: 320px;
            height: 100vh;
            max-height: 100vh;
            background: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            border-radius: 0;
            box-shadow: none;
            overflow: hidden;
            position: relative;
            border-right: 1px solid var(--border-glow);
        }
        
        /* Subtle noise texture for depth */
        .sidebar-container::before {
            content: "";
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiGAAAAA1BMVEX///+nxBvIAAAANElEQVR42u3BAQ0AAADCoPdPbQ8HFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPBrxWIAAQqkhDkAAAAASUVORK5CYII=');
            opacity: 0.03;
            pointer-events: none;
        }

        /* Header */
        .header {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: inset 0 -1px 0 0 var(--border-glow);
            flex-shrink: 0;
            position: relative;
            z-index: 2;
        }
        .header h4 { margin: 0 0 0 8px; font-weight: 600; font-size: 16px; }
        .header-title { display: flex; align-items: center; }
        .header-title .fluent-icon { font-size: 20px; color: var(--accent-purple); }
        .header-status { display: flex; align-items: center; gap: 16px; font-size: 12px; color: var(--text-secondary); }
        .status-indicator { display: flex; align-items: center; gap: 6px; }
        .status-dot { width: 8px; height: 8px; border-radius: 50%; background-color: var(--accent-green); animation: pulse-green 2s infinite; }
        .status-dot.error { background-color: var(--accent-red); animation: pulse-red 2s infinite; }
        .status-dot.stopped { background-color: var(--text-secondary); animation: none; }

        @keyframes pulse-green { from { box-shadow: 0 0 0 0 #37A27699; } to { box-shadow: 0 0 0 8px #37A27600; } }
        @keyframes pulse-red { from { box-shadow: 0 0 0 0 #D1343899; } to { box-shadow: 0 0 0 8px #D1343800; } }

        /* Transcription Area */
        .transcription-container {
            flex: 1;
            overflow-y: auto;
            padding: 8px 20px;
            scroll-behavior: smooth;
        }
        .transcription-container::-webkit-scrollbar { width: 8px; }
        .transcription-container::-webkit-scrollbar-track { background: transparent; }
        .transcription-container::-webkit-scrollbar-thumb { background: var(--bg-tertiary); border-radius: 4px; }
        .transcription-container::-webkit-scrollbar-thumb:hover { background: var(--text-secondary); }
        
        /* Transcription Segment */
        .transcription-segment {
            padding: 4px 0;
            display: flex;
            flex-direction: column;
            gap: 2px;
            transition: opacity 0.3s ease;
            animation: fadeIn 0.5s cubic-bezier(0.215, 0.610, 0.355, 1);
        }
        .transcription-segment.partial { opacity: 0.6; }

        /* New Speaker Indicator */
        .transcription-segment.new-speaker {
            margin-top: 16px;
            padding-left: 16px;
            border-left: 2px solid var(--accent-purple);
        }
        .transcription-segment.new-speaker:first-child { margin-top: 0; }

        .speaker-name { font-weight: 600; font-size: 14px; color: var(--text-primary); }
        .segment-text { font-size: 14px; line-height: 1.6; color: var(--text-secondary); }
        .new-speaker .speaker-name { color: var(--accent-purple); }

        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        
        /* Empty State */
        .empty-state { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; color: var(--text-secondary); }
        .empty-state .fluent-icon { font-size: 48px; margin-bottom: 16px; opacity: 0.3; }
        .empty-state h5 { margin: 0 0 8px 0; font-weight: 600; font-size: 16px; color: var(--text-primary); }
        .empty-state p { margin: 0; font-size: 14px; max-width: 250px; line-height: 1.5; }

        /* Bottom Controls */
        .bottom-controls {
            display: flex; align-items: center; justify-content: space-between;
            padding: 10px 16px; background-color: var(--bg-secondary);
            box-shadow: inset 0 1px 0 0 var(--border-glow);
            flex-shrink: 0; gap: 16px; position: relative; z-index: 2;
        }
        
        .language-selector-container { display: flex; align-items: center; gap: 8px; }
        .language-selector-container .fluent-icon { font-size: 16px; color: var(--text-secondary); }
        #languageSelect {
            background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-glow);
            border-radius: var(--radius-sm); padding: 6px 8px; font-size: 12px; outline: none; transition: all 0.2s;
        }
        #languageSelect:hover { border-color: var(--text-secondary); }
        #languageSelect:focus { border-color: var(--accent-blue); }
        #languageSelect:disabled { opacity: 0.5; cursor: not-allowed; }
        
        /* Control Buttons */
        .btn-group-bottom { display: flex; gap: 8px; }
        .btn {
            display: flex; align-items: center; justify-content: center;
            width: 36px; height: 36px; border: none; border-radius: var(--radius-sm);
            background-color: var(--bg-tertiary); color: var(--text-primary);
            cursor: pointer; outline: none; transition: all 0.2s ease;
        }
        .btn .fluent-icon { font-size: 18px; }
        .btn:hover:not(:disabled) { transform: translateY(-2px); box-shadow: 0 4px 10px rgba(0,0,0,0.2); }
        .btn:focus-visible { box-shadow: 0 0 0 2px var(--bg-primary), 0 0 0 4px var(--accent-blue); }
        .btn#startBtn:not(:disabled) { background-color: var(--accent-green); animation: pulse-start 2s infinite; }
        .btn#stopBtn:not(:disabled) { background-color: var(--accent-red); }
        .btn#exportBtn:not(:disabled) { background-color: var(--accent-blue); }
        .btn:disabled { background-color: var(--bg-tertiary); opacity: 0.4; cursor: not-allowed; }

        @keyframes pulse-start {
             0% { box-shadow: 0 0 0 0 rgba(55, 162, 118, 0.7); }
             70% { box-shadow: 0 0 0 10px rgba(55, 162, 118, 0); }
             100% { box-shadow: 0 0 0 0 rgba(55, 162, 118, 0); }
        }

        /* Error Message */
        .error-message { background: #4B2A2A; color: #FFCACA; padding: 12px 16px; margin: 0 16px 16px 16px; border-radius: var(--radius-md); border: 1px solid var(--accent-red); font-size: 14px; text-align: center; }
    </style>
</head>
<body>
    <div class="sidebar-container">
        <div class="header">
            <div class="header-title">
                <span class="fluent-icon">&#xE720;</span>
                <h4>AccureMD</h4>
            </div>
            <div class="header-status">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ready</span>
                </div>
                <div class="session-info">
                     <span id="sessionDuration">00:00</span>
                </div>
            </div>
        </div>

        <div id="errorMessageContainer"></div>

        <div class="transcription-container" id="transcriptionContainer">
            <div class="empty-state" id="emptyState">
                <span class="fluent-icon">&#xE785;</span>
                <h5>Meeting Transcription</h5>
                <p>Click the start button to begin capturing the conversation.</p>
            </div>
        </div>

        <div class="bottom-controls">
            <div class="language-selector-container">
                 <span class="fluent-icon">&#xE713;</span>
                 <select id="languageSelect" title="Select Language">
                     <option value="en-US">English (US)</option>
                     <option value="en-GB">English (UK)</option>
                     <option value="es-ES">Spanish</option>
                 </select>
            </div>
            <div class="btn-group-bottom">
                 <button class="btn" id="startBtn" onclick="startTranscription()" title="Start Transcription">
                     <span class="fluent-icon">&#xE768;</span>
                 </button>
                 <button class="btn" id="stopBtn" onclick="stopTranscription()" disabled title="Stop Transcription">
                     <span class="fluent-icon">&#xE71A;</span>
                 </button>
                 <button class="btn" id="exportBtn" onclick="exportTranscription()" disabled title="Export Transcript">
                     <span class="fluent-icon">&#xE896;</span>
                 </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isTranscribing = false;
        let sessionStartTime = null;
        let segmentCount = 0;
        let durationInterval = null;
        let demoInterval = null;
        let lastSpeakerName = null; // To track speaker changes

        // DOM Elements
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const exportBtn = document.getElementById('exportBtn');
        const languageSelect = document.getElementById('languageSelect');
        const transcriptionContainer = document.getElementById('transcriptionContainer');
        const emptyState = document.getElementById('emptyState');
        const statusText = document.getElementById('statusText');
        const statusDot = document.getElementById('statusDot');
        const sessionDurationEl = document.getElementById('sessionDuration');
        const errorMessageContainer = document.getElementById('errorMessageContainer');
        
        // --- CORE LOGIC (UNCHANGED) ---

        // Start/Stop Transcription
        function startTranscription() {
            if (isTranscribing) return;
            isTranscribing = true;
            sessionStartTime = new Date();
            
            clearTranscription();
            startDurationTimer();
            updateUI();
            updateStatus('Transcribing', 'active');
            startDemoTranscription();
        }

        function stopTranscription() {
            if (!isTranscribing) return;
            isTranscribing = false;
            
            stopDurationTimer();
            stopDemoTranscription();
            updateUI();
            updateStatus('Stopped', 'stopped');
            
            const partial = transcriptionContainer.querySelector('.partial');
            if (partial) {
                partial.classList.remove('partial');
            }
        }
        
        // --- UI & DISPLAY LOGIC (MODIFIED FOR AESTHETICS) ---
        
        function updateUI() {
            startBtn.disabled = isTranscribing;
            stopBtn.disabled = !isTranscribing;
            languageSelect.disabled = isTranscribing;
            exportBtn.disabled = !isTranscribing && segmentCount === 0;

            // Toggle pulsing animation on start button
            if (!isTranscribing && segmentCount === 0) {
                 startBtn.style.animation = 'pulse-start 2s infinite';
            } else {
                 startBtn.style.animation = 'none';
            }
        }
        
        function updateStatus(text, type) {
            statusText.textContent = text;
            statusDot.className = 'status-dot';
            if (type === 'error' || type === 'stopped' || type === 'active') {
                statusDot.classList.add(type);
            }
        }
        
        function showError(message) {
            errorMessageContainer.innerHTML = `<div class="error-message">${message}</div>`;
            setTimeout(() => { errorMessageContainer.innerHTML = ''; }, 5000);
        }

        let currentPartialSegment = null;
        
        function addTranscriptionSegment(segment) {
            if (emptyState.parentElement) {
                emptyState.remove();
            }

            if (segment.isFinal) {
                if (currentPartialSegment && currentPartialSegment.dataset.segmentId === segment.segmentId) {
                    updateSegmentContent(currentPartialSegment, segment);
                    currentPartialSegment.classList.remove('partial');
                    checkSpeakerChange(currentPartialSegment, segment.speakerName);
                    currentPartialSegment = null;
                } else {
                    if (currentPartialSegment) currentPartialSegment.remove();
                    createFinalSegment(segment);
                }
            } else {
                if (currentPartialSegment && currentPartialSegment.dataset.segmentId === segment.segmentId) {
                    updateSegmentContent(currentPartialSegment, segment);
                } else {
                    if (currentPartialSegment) currentPartialSegment.remove();
                    currentPartialSegment = createSegmentElement(segment, true);
                    checkSpeakerChange(currentPartialSegment, segment.speakerName);
                    transcriptionContainer.appendChild(currentPartialSegment);
                }
            }
            transcriptionContainer.scrollTop = transcriptionContainer.scrollHeight;
        }
        
        function createFinalSegment(segment) {
             const segmentElement = createSegmentElement(segment, false);
             checkSpeakerChange(segmentElement, segment.speakerName);
             transcriptionContainer.appendChild(segmentElement);
             segmentCount++;
             lastSpeakerName = segment.speakerName;
             updateUI();
        }

        function createSegmentElement(segment, isPartial) {
            const el = document.createElement('div');
            el.className = `transcription-segment ${isPartial ? 'partial' : ''}`;
            el.dataset.segmentId = segment.segmentId;
            updateSegmentContent(el, segment);
            return el;
        }

        function checkSpeakerChange(element, speakerName) {
            if (speakerName !== lastSpeakerName) {
                element.classList.add('new-speaker');
            }
        }

        function updateSegmentContent(element, segment) {
            element.innerHTML = `
                <span class="speaker-name">${segment.speakerName || 'Speaker'}</span>
                <span class="segment-text">${segment.text}</span>
            `;
        }
        
        function clearTranscription() {
            transcriptionContainer.innerHTML = '';
            transcriptionContainer.appendChild(emptyState);
            segmentCount = 0;
            currentPartialSegment = null;
            lastSpeakerName = null;
            sessionDurationEl.textContent = '00:00';
            updateUI();
        }
        
        // --- TIMERS & EXPORT (UNCHANGED) ---
        
        function startDurationTimer() {
            stopDurationTimer();
            durationInterval = setInterval(updateDuration, 1000);
        }

        function stopDurationTimer() { clearInterval(durationInterval); }

        function updateDuration() {
            if (!sessionStartTime) return;
            const elapsed = Math.floor((new Date() - sessionStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            sessionDurationEl.textContent = `${minutes}:${seconds}`;
        }
        
        function exportTranscription() {
            const segments = transcriptionContainer.querySelectorAll('.transcription-segment:not(.partial)');
            if (segments.length === 0) {
                showError("No final transcription to export.");
                return;
            }
            let exportText = `AccureMD Transcription - ${new Date().toLocaleString()}\n\n`;
            segments.forEach(seg => {
                const speaker = seg.querySelector('.speaker-name').textContent;
                const text = seg.querySelector('.segment-text').textContent;
                exportText += `[${speaker}]: ${text}\n`;
            });
            const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = `AccureMD_Transcription.txt`;
            a.click();
            URL.revokeObjectURL(a.href);
        }

        // --- DEMO FUNCTIONALITY (UNCHANGED) ---

        const demoSegments = [
            { text: "Okay team, let's kick off the quarterly review.", speaker: "Michael Cash" },
            { text: "As you can see from the projection charts, our growth has been steady.", speaker: "Michael Cash" },
            { text: "Sarah, can you speak to the new marketing campaign?", speaker: "Michael Cash" },
            { text: "Certainly. The campaign 'Innovate Forward' has seen a 20% increase in user engagement across all platforms.", speaker: "Sarah Johnson" },
            { text: "Our cost-per-acquisition is down by 15%, which is fantastic news.", speaker: "Sarah Johnson" },
            { text: "Excellent work, Sarah. Let's make sure we have the budget allocated for the next phase.", speaker: "Michael Cash" },
            { text: "I'll get on that right away and send the proposal by end of day.", speaker: "John Smith" },
            { text: "I have a question about the server infrastructure for the new feature.", speaker: "John Smith" }
        ];
        let demoIndex = 0;
        let demoStreamInterval = null;

        function startDemoTranscription() {
            stopDemoTranscription();
            demoIndex = 0;
            simulateRealTimeTranscription();
        }

        function stopDemoTranscription() {
            clearInterval(demoInterval);
            clearInterval(demoStreamInterval);
        }

        function simulateRealTimeTranscription() {
            if (!isTranscribing || demoIndex >= demoSegments.length) {
                stopDemoTranscription();
                return;
            };

            const fullSegment = demoSegments[demoIndex];
            const words = fullSegment.text.split(' ');
            let wordIndex = 0;

            demoStreamInterval = setInterval(() => {
                if (!isTranscribing) { stopDemoTranscription(); return; }

                wordIndex++;
                const isComplete = wordIndex >= words.length;
                const partialText = words.slice(0, wordIndex).join(' ');

                addTranscriptionSegment({
                    segmentId: `demo-${demoIndex}`,
                    speakerName: fullSegment.speaker,
                    text: partialText,
                    isFinal: isComplete,
                });

                if (isComplete) {
                    clearInterval(demoStreamInterval);
                    demoIndex++;
                    demoInterval = setTimeout(simulateRealTimeTranscription, 1500 + Math.random() * 1000);
                }
            }, 120);
        }

        document.addEventListener('DOMContentLoaded', () => { updateUI(); });
    </script>
</body>
</html>
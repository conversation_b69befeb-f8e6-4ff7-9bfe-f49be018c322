using AccureMD.Core.Interfaces;
using AccureMD.Services.Implementation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

var builder = WebApplication.CreateBuilder(args);

// Add configuration
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddUserSecrets<Program>();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add AccureMD Services
builder.Services.AddScoped<IMeetingService, MeetingService>();
builder.Services.AddScoped<ITranscriptionService, TranscriptionService>();
builder.Services.AddScoped<IRecordingService, RecordingService>();

// HTTP Client for external services
builder.Services.AddHttpClient();

// SignalR for real-time communication
builder.Services.AddSignalR();

// Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.Authority = "https://login.microsoftonline.com/common/v2.0";
        options.Audience = builder.Configuration["Bot:MicrosoftAppId"];
        options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true
        };
    });

// CORS configuration for Teams
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowTeams", policy =>
    {
        policy.WithOrigins(
                "https://teams.microsoft.com",
                "https://*.teams.microsoft.com",
                "https://teams.live.com",
                "https://*.teams.live.com"
            )
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowTeams");
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// SignalR hub for real-time transcription
// app.MapHub<TranscriptionHub>("/transcriptionHub"); // TODO: Create TranscriptionHub in Web project

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { status = "healthy", timestamp = DateTime.UtcNow }));

// Teams app configuration endpoints
app.MapGet("/config", () => Results.Redirect("/static/config.html"));
app.MapGet("/transcription", () => Results.Redirect("/static/transcription.html"));
app.MapGet("/meeting-sidebar", () => Results.Redirect("/static/meeting-sidebar.html"));

app.Run();

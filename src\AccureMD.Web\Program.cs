using AccureMD.Core.Interfaces;
using AccureMD.Services.Implementation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

var builder = WebApplication.CreateBuilder(args);

// Add configuration
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddUserSecrets<Program>();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add AccureMD Services
builder.Services.AddScoped<IMeetingService, MeetingService>();
builder.Services.AddScoped<ITranscriptionService, TranscriptionService>();
builder.Services.AddScoped<IRecordingService, RecordingService>();

// HTTP Client for external services
builder.Services.AddHttpClient();

// SignalR for real-time communication
builder.Services.AddSignalR();

// Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.Authority = "https://login.microsoftonline.com/common/v2.0";
        options.Audience = builder.Configuration["Bot:MicrosoftAppId"];
        options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true
        };
    });

// CORS configuration for Teams
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowTeams", policy =>
    {
        policy.WithOrigins(
                "https://teams.microsoft.com",
                "https://*.teams.microsoft.com",
                "https://teams.live.com",
                "https://*.teams.live.com"
            )
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowTeams");
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// SignalR hub for real-time transcription
// app.MapHub<TranscriptionHub>("/transcriptionHub"); // TODO: Create TranscriptionHub in Web project

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { status = "healthy", timestamp = DateTime.UtcNow }));

// Teams app configuration endpoints
app.MapGet("/config", () => Results.Redirect("/static/config.html"));
app.MapGet("/transcription", () => Results.Redirect("/static/meeting-sidebar.html"));
app.MapGet("/meeting-sidebar", () => Results.Redirect("/static/meeting-sidebar.html"));

// Legal pages required by Teams manifest
app.MapGet("/privacy", () => Results.Content(@"
<!DOCTYPE html>
<html><head><title>AccureMD Privacy Policy</title></head>
<body style='font-family: Segoe UI; padding: 20px; max-width: 800px; margin: 0 auto;'>
<h1>AccureMD Privacy Policy</h1>
<p>Last updated: August 7, 2025</p>
<h2>Information We Collect</h2>
<p>AccureMD collects meeting transcription data to provide real-time transcription services.</p>
<h2>How We Use Information</h2>
<p>We use transcription data solely to provide transcription services and do not store or share this data.</p>
<h2>Data Security</h2>
<p>All data is processed securely and in compliance with Microsoft Teams security standards.</p>
<h2>Contact Us</h2>
<p>For privacy questions, contact <NAME_EMAIL></p>
</body></html>", "text/html"));

app.MapGet("/terms", () => Results.Content(@"
<!DOCTYPE html>
<html><head><title>AccureMD Terms of Service</title></head>
<body style='font-family: Segoe UI; padding: 20px; max-width: 800px; margin: 0 auto;'>
<h1>AccureMD Terms of Service</h1>
<p>Last updated: August 7, 2025</p>
<h2>Acceptance of Terms</h2>
<p>By using AccureMD, you agree to these terms of service.</p>
<h2>Service Description</h2>
<p>AccureMD provides real-time meeting transcription services for Microsoft Teams.</p>
<h2>User Responsibilities</h2>
<p>Users are responsible for ensuring they have permission to record and transcribe meetings.</p>
<h2>Limitation of Liability</h2>
<p>AccureMD is provided as-is without warranties of any kind.</p>
<h2>Contact Us</h2>
<p>For questions about these terms, contact <NAME_EMAIL></p>
</body></html>", "text/html"));

app.Run();

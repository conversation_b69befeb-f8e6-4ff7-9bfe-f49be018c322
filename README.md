# AccureMD - Microsoft Teams Transcription Extension

AccureMD is a production-ready Microsoft Teams extension that provides real-time meeting transcription capabilities similar to Read AI. The application is built in C# and includes comprehensive features for recording, transcribing, and managing meeting content with enterprise-grade security and compliance.

## 🚀 Features

### Core Functionality
- **Real-time Transcription**: Live speech-to-text conversion during Teams meetings
- **Meeting Recording**: Audio and video recording with multiple stream support
- **Speaker Identification**: Advanced speaker diarization for multi-participant meetings
- **Live Sidebar UI**: In-meeting interface for viewing transcriptions in real-time
- **Automatic Meeting Join**: <PERSON><PERSON> can automatically join scheduled meetings

### Technical Capabilities
- **Voxtral Integration Ready**: Placeholder architecture for Voxtral ASR model integration
- **Multi-language Support**: Configurable language settings for transcription
- **Cloud Storage**: Azure Blob Storage integration for recordings and transcripts
- **Real-time Updates**: SignalR-powered live transcription streaming
- **RESTful API**: Comprehensive API for all transcription and recording operations

### Security & Compliance
- **Enterprise Authentication**: Microsoft Graph API integration with proper permissions
- **Data Protection**: PII redaction and sensitive information filtering
- **Compliance Logging**: Comprehensive audit trails for regulatory requirements
- **Encryption**: Data encryption for sensitive information storage
- **Permission Management**: Role-based access control for meeting operations

## 📁 Project Structure

```
AccureMD/
├── src/
│   ├── AccureMD.Core/           # Core models, interfaces, and business logic
│   ├── AccureMD.Services/       # Service implementations and data access
│   ├── AccureMD.Bot/           # Teams bot implementation
│   └── AccureMD.Web/           # Web API and UI components
├── deployment/                  # Teams app manifest and deployment files
├── tests/                      # Unit and integration tests (placeholder)
└── docs/                       # Additional documentation
```

## 🛠️ Prerequisites

- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Azure subscription (for deployment)
- Microsoft Teams Developer account
- Azure Bot Service registration
- Azure AD app registration

## ⚙️ Configuration

### 1. Azure Bot Service Setup

1. Create a new Bot Service in Azure Portal
2. Note the Microsoft App ID and generate an App Password
3. Configure the messaging endpoint: `https://your-domain.azurewebsites.net/api/messages`

### 2. Azure AD App Registration

1. Register a new application in Azure AD
2. Add required API permissions:
   - Microsoft Graph: OnlineMeetings.ReadWrite
   - Microsoft Graph: Calls.AccessMedia.All
   - Microsoft Graph: Calls.JoinGroupCall.All
3. Generate client secret

### 3. Application Configuration

Update the configuration files with your values:

#### `src/AccureMD.Bot/appsettings.json`
```json
{
  "Bot": {
    "MicrosoftAppId": "YOUR_BOT_APP_ID",
    "MicrosoftAppPassword": "YOUR_BOT_APP_PASSWORD",
    "MicrosoftAppTenantId": "YOUR_TENANT_ID",
    "BaseUrl": "https://your-domain.azurewebsites.net"
  },
  "Database": {
    "ConnectionString": "YOUR_DATABASE_CONNECTION_STRING",
    "Provider": "SqlServer"
  },
  "Azure": {
    "Storage": {
      "ConnectionString": "YOUR_STORAGE_CONNECTION_STRING"
    }
  }
}
```

#### `deployment/manifest.json`
Replace the following placeholders:
- `{{TEAMS_APP_ID}}`: Your Teams app ID
- `{{BOT_ID}}`: Your bot's Microsoft App ID
- `{{AAD_APP_ID}}`: Your Azure AD application ID
- `{{DOMAIN_NAME}}`: Your application domain

### 4. Voxtral Integration (Optional)

To integrate with Voxtral ASR model, update the configuration:

```json
{
  "Bot": {
    "Transcription": {
      "Voxtral": {
        "ApiEndpoint": "YOUR_VOXTRAL_API_ENDPOINT",
        "ApiKey": "YOUR_VOXTRAL_API_KEY",
        "ModelVersion": "latest"
      }
    }
  }
}
```

## 🚀 Deployment

### Local Development

1. Clone the repository
2. Update configuration files
3. Run the application:

```bash
cd src/AccureMD.Bot
dotnet run
```

### Azure Deployment

1. **Deploy to Azure App Service**:
   ```bash
   # Build and publish
   dotnet publish -c Release -o ./publish
   
   # Deploy to Azure (using Azure CLI)
   az webapp deployment source config-zip \
     --resource-group your-resource-group \
     --name your-app-name \
     --src publish.zip
   ```

2. **Create Teams App Package**:
   ```bash
   cd deployment
   npm install
   npm run package
   ```

3. **Upload to Teams**:
   - Go to Teams Admin Center
   - Navigate to Teams apps > Manage apps
   - Upload the generated `AccureMD-Teams-App.zip`

## 📋 API Documentation

### Transcription Endpoints

#### Start Transcription
```http
POST /api/transcription/start
Content-Type: application/json

{
  "meetingId": "meeting-123",
  "language": "en-US",
  "enableSpeakerDiarization": true,
  "enablePunctuation": true
}
```

#### Stop Transcription
```http
POST /api/transcription/stop/{sessionId}
```

#### Get Transcription Segments
```http
GET /api/transcription/session/{sessionId}/segments?skip=0&take=50
```

### Recording Endpoints

#### Start Recording
```http
POST /api/recording/start
Content-Type: application/json

{
  "meetingId": "meeting-123",
  "recordAudio": true,
  "recordVideo": true,
  "recordScreenShare": true
}
```

#### Stop Recording
```http
POST /api/recording/stop/{recordingId}
```

## 🔧 Development

### Building the Solution

```bash
# Restore packages
dotnet restore

# Build solution
dotnet build

# Run tests
dotnet test

# Run specific project
cd src/AccureMD.Bot
dotnet run
```

### Database Migrations

```bash
# Add migration
dotnet ef migrations add InitialCreate -p src/AccureMD.Services -s src/AccureMD.Bot

# Update database
dotnet ef database update -p src/AccureMD.Services -s src/AccureMD.Bot
```

## 🧪 Testing

### Unit Tests
```bash
dotnet test tests/AccureMD.Tests.Unit
```

### Integration Tests
```bash
dotnet test tests/AccureMD.Tests.Integration
```

### Manual Testing
1. Start the application locally
2. Use ngrok to expose local endpoint: `ngrok http 5000`
3. Update bot messaging endpoint to ngrok URL
4. Test in Teams

## 📊 Monitoring and Logging

The application includes comprehensive logging and monitoring:

- **Application Insights**: Performance and error tracking
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Health Checks**: Built-in health check endpoints
- **Compliance Auditing**: Detailed audit logs for regulatory compliance

## 🔒 Security Considerations

- All sensitive data is encrypted at rest and in transit
- PII redaction is applied to transcription data
- Role-based access control for all operations
- Compliance validation before recording/transcription
- Security headers and CORS policies implemented

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `docs/` folder
- Review the troubleshooting guide

## 🔄 Roadmap

- [ ] Advanced speaker recognition
- [ ] Meeting summary generation
- [ ] Multi-language real-time translation
- [ ] Integration with additional ASR providers
- [ ] Mobile app support
- [ ] Advanced analytics and reporting

---

**Note**: This is a production-ready foundation with placeholder integrations for Voxtral ASR model and external services. The architecture is designed to easily accommodate these integrations when ready.




Deployment status: 
PS D:\iData Project\AccureMD> az webapp deploy --resource-group AccureMD --name accuremd --src-path "D:\iData Project\AccureMD\publish\accuremd.zip" --type zip
>>
Initiating deployment
Deploying from local path: D:\iData Project\AccureMD\publish\accuremd.zip
Polling the status of sync deployment. Start Time: 2025-08-07 16:02:29.510582+00:00 UTC
Deployment has completed successfully
You can visit your app at: http://accuremd.azurewebsites.net
{
  "active": true,
  "author": "N/A",
  "author_email": "N/A",
  "complete": true,
  "deployer": "OneDeploy",
  "end_time": "2025-08-07T16:02:28.3603313Z",
  "id": "00e1bd0cb0a9413b93ec47f31564c233",
  "is_readonly": true,
  "is_temp": false,
  "last_success_end_time": "2025-08-07T16:02:28.3603313Z",
  "log_url": "https://accuremd.scm.azurewebsites.net/api/deployments/latest/log",
  "message": "OneDeploy",
  "progress": "",
  "provisioningState": "Succeeded",
  "received_time": "2025-08-07T16:02:24.7146608Z",
  "site_name": "accuremd",
  "start_time": "2025-08-07T16:02:24.839668Z",
  "status": 4,
  "status_text": "",
  "url": "https://accuremd.scm.azurewebsites.net/api/deployments/latest"
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccureMD - Meeting Transcription</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Fluent UI Icons - Using Segoe Fluent Icons font -->
    <style>
        @font-face {
            font-family: 'Segoe Fluent Icons';
            src: local('Segoe Fluent Icons');
        }

        .fluent-icon {
            font-family: 'Segoe Fluent Icons', 'Segoe UI Symbol', sans-serif;
            font-style: normal;
            font-weight: normal;
            speak: none;
            display: inline-block;
            text-decoration: inherit;
            width: 1em;
            text-align: center;
            font-variant: normal;
            text-transform: none;
            line-height: 1em;
        }
    </style>
    <style>
        /* Microsoft Teams Design System Variables */
        :root {
            /* Teams Color Palette */
            --teams-purple: #6264A7;
            --teams-purple-dark: #464775;
            --teams-purple-light: #8B8CC7;
            --teams-blue: #0078D4;
            --teams-blue-dark: #106EBE;
            --teams-gray-900: #252423;
            --teams-gray-800: #323130;
            --teams-gray-700: #484644;
            --teams-gray-600: #605E5C;
            --teams-gray-500: #797775;
            --teams-gray-400: #A19F9D;
            --teams-gray-300: #C8C6C4;
            --teams-gray-200: #EDEBE9;
            --teams-gray-100: #F3F2F1;
            --teams-gray-50: #FAF9F8;
            --teams-white: #FFFFFF;

            /* Semantic Colors */
            --teams-success: #107C10;
            --teams-warning: #FF8C00;
            --teams-error: #D13438;
            --teams-info: #0078D4;

            /* Typography */
            --teams-font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
            --teams-font-size-100: 12px;
            --teams-font-size-200: 14px;
            --teams-font-size-300: 16px;
            --teams-font-size-400: 18px;
            --teams-font-size-500: 20px;
            --teams-font-size-600: 24px;

            /* Spacing */
            --teams-space-xs: 4px;
            --teams-space-sm: 8px;
            --teams-space-md: 12px;
            --teams-space-lg: 16px;
            --teams-space-xl: 20px;
            --teams-space-xxl: 24px;

            /* Border Radius */
            --teams-radius-sm: 4px;
            --teams-radius-md: 6px;
            --teams-radius-lg: 8px;

            /* Shadows */
            --teams-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
            --teams-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
            --teams-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
        }

        body {
            font-family: var(--teams-font-family);
            margin: 0;
            padding: 0;
            background-color: var(--teams-gray-50);
            height: 100vh;
            overflow: hidden;
            font-size: var(--teams-font-size-200);
            color: var(--teams-gray-900);
            line-height: 1.4;
            /* Teams integration optimizations */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .sidebar-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100%;
            max-width: 480px;
            min-width: 320px;
            background: var(--teams-white);
            border-left: 1px solid var(--teams-gray-300);
            box-shadow: var(--teams-shadow-md);
            overflow: hidden;
        }

        .header {
            background: var(--teams-purple);
            color: var(--teams-white);
            padding: var(--teams-space-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--teams-shadow-sm);
            border-bottom: 1px solid var(--teams-purple-dark);
        }

        .header h4 {
            margin: 0;
            font-weight: 600;
            font-size: var(--teams-font-size-400);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--teams-space-sm);
            font-size: var(--teams-font-size-100);
            font-weight: 500;
        }

        .status-dot {
            width: var(--teams-space-sm);
            height: var(--teams-space-sm);
            border-radius: 50%;
            background-color: var(--teams-success);
            animation: pulse 2s infinite;
        }

        .status-dot.error {
            background-color: var(--teams-error);
        }

        .status-dot.warning {
            background-color: var(--teams-warning);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .language-selector-top {
            padding: var(--teams-space-lg);
            border-bottom: 1px solid var(--teams-gray-200);
            background: var(--teams-gray-50);
        }

        .language-selector-top label {
            display: block;
            margin-bottom: var(--teams-space-xs);
            font-weight: 600;
            color: var(--teams-gray-800);
            font-size: var(--teams-font-size-200);
        }

        .language-selector-top select {
            width: 100%;
            padding: var(--teams-space-sm) var(--teams-space-md);
            border: 1px solid var(--teams-gray-300);
            border-radius: var(--teams-radius-md);
            font-size: var(--teams-font-size-200);
            font-family: var(--teams-font-family);
            background: var(--teams-white);
            color: var(--teams-gray-900);
            transition: border-color 0.15s ease-in-out;
        }

        .language-selector-top select:focus {
            outline: none;
            border-color: var(--teams-blue);
            box-shadow: 0 0 0 1px var(--teams-blue);
        }

        .btn-group {
            display: flex;
            gap: var(--teams-space-sm);
            margin-bottom: var(--teams-space-md);
        }

        /* Teams-style buttons */
        .btn {
            flex: 1;
            padding: var(--teams-space-sm) var(--teams-space-md);
            border: 1px solid transparent;
            border-radius: var(--teams-radius-md);
            font-size: var(--teams-font-size-200);
            font-weight: 600;
            font-family: var(--teams-font-family);
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--teams-space-xs);
            min-height: 32px;
            text-decoration: none;
            outline: none;
        }

        .btn:focus {
            outline: 2px solid var(--teams-blue);
            outline-offset: 1px;
            box-shadow: 0 0 0 1px var(--teams-blue);
        }

        .btn:focus:not(:focus-visible) {
            outline: none;
            box-shadow: none;
        }

        .btn:focus-visible {
            outline: 2px solid var(--teams-blue);
            outline-offset: 1px;
            box-shadow: 0 0 0 1px var(--teams-blue);
        }

        .btn-primary {
            background: var(--teams-purple);
            color: var(--teams-white);
            border-color: var(--teams-purple);
        }

        .btn-primary:hover {
            background: var(--teams-purple-dark);
            border-color: var(--teams-purple-dark);
        }

        .btn-success {
            background: var(--teams-success);
            color: var(--teams-white);
            border-color: var(--teams-success);
        }

        .btn-success:hover {
            background: #0E6E0E;
            border-color: #0E6E0E;
        }

        .btn-danger {
            background: var(--teams-error);
            color: var(--teams-white);
            border-color: var(--teams-error);
        }

        .btn-danger:hover {
            background: #B02E31;
            border-color: #B02E31;
        }

        .btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: var(--teams-gray-300);
            color: var(--teams-gray-600);
            border-color: var(--teams-gray-300);
        }



        .transcription-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: var(--teams-space-lg);
            background: var(--teams-white);
            scroll-behavior: smooth;
            /* Custom scrollbar for Teams consistency */
            scrollbar-width: thin;
            scrollbar-color: var(--teams-gray-400) var(--teams-gray-100);
        }

        .transcription-container::-webkit-scrollbar {
            width: 8px;
        }

        .transcription-container::-webkit-scrollbar-track {
            background: var(--teams-gray-100);
        }

        .transcription-container::-webkit-scrollbar-thumb {
            background: var(--teams-gray-400);
            border-radius: 4px;
        }

        .transcription-container::-webkit-scrollbar-thumb:hover {
            background: var(--teams-gray-600);
        }

        .transcription-segment {
            margin-bottom: var(--teams-space-lg);
            padding: var(--teams-space-md);
            border-radius: var(--teams-radius-lg);
            background: var(--teams-gray-50);
            border-left: 4px solid var(--teams-purple);
            animation: fadeIn 0.3s ease-in;
            box-shadow: var(--teams-shadow-sm);
            transition: all 0.3s ease-in-out;
        }

        .transcription-segment.partial {
            background: var(--teams-gray-100);
            border-left-color: var(--teams-warning);
            opacity: 0.9;
        }

        .transcription-segment.final {
            background: var(--teams-white);
            border-left-color: var(--teams-purple);
        }

        .partial-indicator {
            color: var(--teams-warning);
            font-size: var(--teams-font-size-100);
            animation: pulse 1.5s infinite;
            margin-left: var(--teams-space-xs);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .segment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--teams-space-sm);
        }

        .speaker-name {
            font-weight: 600;
            color: var(--teams-purple);
            font-size: var(--teams-font-size-200);
        }

        .timestamp {
            font-size: var(--teams-font-size-100);
            color: var(--teams-gray-600);
        }

        .segment-text {
            line-height: 1.5;
            color: var(--teams-gray-900);
            font-size: var(--teams-font-size-200);
        }

        .confidence-indicator {
            margin-top: var(--teams-space-sm);
            display: flex;
            align-items: center;
            gap: var(--teams-space-sm);
            font-size: var(--teams-font-size-100);
            color: var(--teams-gray-600);
        }

        .confidence-bar {
            flex: 1;
            height: 4px;
            background: var(--teams-gray-200);
            border-radius: var(--teams-radius-sm);
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: var(--teams-radius-sm);
        }

        .confidence-fill.high-confidence {
            background: var(--teams-success);
        }

        .confidence-fill.medium-confidence {
            background: var(--teams-warning);
        }

        .confidence-fill.low-confidence {
            background: var(--teams-error);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: var(--teams-gray-600);
            padding: var(--teams-space-xxl);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--teams-space-lg);
            opacity: 0.6;
            color: var(--teams-gray-400);
        }

        .empty-state h5 {
            margin: 0 0 var(--teams-space-sm) 0;
            font-weight: 600;
            font-size: var(--teams-font-size-300);
            color: var(--teams-gray-800);
        }

        .empty-state p {
            margin: 0;
            font-size: var(--teams-font-size-200);
            opacity: 0.8;
            color: var(--teams-gray-600);
        }

        .session-info {
            padding: var(--teams-space-sm) var(--teams-space-lg);
            border-top: 1px solid var(--teams-gray-200);
            background: var(--teams-gray-50);
            font-size: var(--teams-font-size-100);
            color: var(--teams-gray-600);
            text-align: center;
        }

        .bottom-controls {
            padding: var(--teams-space-lg);
            border-top: 1px solid var(--teams-gray-200);
            background: var(--teams-white);
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-group-bottom {
            display: flex;
            gap: var(--teams-space-sm);
            justify-content: center;
        }

        .btn-compact {
            padding: var(--teams-space-xs) var(--teams-space-md);
            min-height: 28px;
            font-size: var(--teams-font-size-100);
            font-weight: 600;
            border-radius: var(--teams-radius-sm);
            flex: 0 0 auto;
            min-width: 80px;
        }

        .btn-compact .fluent-icon {
            font-size: 12px;
            margin-right: var(--teams-space-xs);
        }

        .header .fluent-icon {
            margin-right: var(--teams-space-sm);
            font-size: var(--teams-font-size-300);
        }

        .empty-state .fluent-icon {
            font-size: 48px;
            margin-bottom: var(--teams-space-lg);
            opacity: 0.6;
            color: var(--teams-gray-400);
        }

        .error-message {
            background: #FDF2F2;
            color: var(--teams-error);
            padding: var(--teams-space-md);
            border-radius: var(--teams-radius-md);
            margin-bottom: var(--teams-space-lg);
            border: 1px solid #F4CCCC;
            font-size: var(--teams-font-size-200);
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--teams-gray-200);
            border-top: 2px solid var(--teams-purple);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Teams-optimized responsive design */
        @media (max-width: 480px) {
            .sidebar-container {
                max-width: 100%;
                min-width: 280px;
            }

            .header {
                padding: var(--teams-space-md);
                flex-wrap: wrap;
            }

            .header h4 {
                font-size: var(--teams-font-size-300);
            }

            .language-selector-top {
                padding: var(--teams-space-md);
            }

            .transcription-container {
                padding: var(--teams-space-md);
            }

            .bottom-controls {
                padding: var(--teams-space-md);
            }

            .btn-group-bottom {
                flex-direction: column;
                gap: var(--teams-space-xs);
            }

            .btn-compact {
                width: 100%;
                min-width: auto;
                padding: var(--teams-space-sm) var(--teams-space-md);
                min-height: 36px;
            }

            .transcription-segment {
                margin-bottom: var(--teams-space-md);
                padding: var(--teams-space-sm);
            }
        }

        /* Extra small screens (Teams narrow sidebar) */
        @media (max-width: 360px) {
            .header h4 {
                font-size: var(--teams-font-size-200);
            }

            .header .fluent-icon {
                font-size: var(--teams-font-size-200);
            }

            .language-selector-top,
            .transcription-container,
            .bottom-controls {
                padding: var(--teams-space-sm);
            }

            .btn-compact {
                font-size: var(--teams-font-size-100);
                padding: var(--teams-space-xs) var(--teams-space-sm);
                min-height: 32px;
            }

            .empty-state .fluent-icon {
                font-size: 36px;
            }

            .empty-state h5 {
                font-size: var(--teams-font-size-200);
            }

            .empty-state p {
                font-size: var(--teams-font-size-100);
            }
        }

        /* Teams sidebar integration optimizations */
        @media (min-width: 481px) and (max-width: 600px) {
            .sidebar-container {
                max-width: 100%;
            }

            .btn-group-bottom {
                justify-content: space-between;
            }

            .btn-compact {
                flex: 1;
                max-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar-container">
        <!-- Header -->
        <div class="header">
            <h4><span class="fluent-icon">&#xE720;</span> AccureMD</h4>
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Ready</span>
            </div>
        </div>

        <!-- Language Selector (moved to top) -->
        <div class="language-selector-top">
            <label for="languageSelect">Language:</label>
            <select id="languageSelect">
                <option value="en-US">English (US)</option>
                <option value="en-GB">English (UK)</option>
                <option value="es-ES">Spanish</option>
                <option value="fr-FR">French</option>
                <option value="de-DE">German</option>
            </select>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="error-message" style="display: none;"></div>

        <!-- Transcription Container -->
        <div class="transcription-container" id="transcriptionContainer">
            <div class="empty-state" id="emptyState">
                <span class="fluent-icon">&#xE74F;</span>
                <h5>No transcription active</h5>
                <p>Select a language and click "Start" to begin real-time transcription</p>
            </div>
        </div>

        <!-- Footer with Session Info -->
        <div class="session-info">
            <span id="segmentCount">0 segments</span> •
            <span id="sessionDuration">00:00</span>
        </div>

        <!-- Bottom Controls -->
        <div class="bottom-controls">
            <div class="btn-group-bottom">
                <button class="btn btn-compact btn-success" id="startBtn" onclick="startTranscription()">
                    <span class="fluent-icon">&#xE768;</span> Start
                </button>
                <button class="btn btn-compact btn-danger" id="stopBtn" onclick="stopTranscription()" disabled>
                    <span class="fluent-icon">&#xE71A;</span> Stop
                </button>
                <button class="btn btn-compact btn-primary" id="exportBtn" onclick="exportTranscription()" disabled>
                    <span class="fluent-icon">&#xE896;</span> Export
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let connection = null;
        let isTranscribing = false;
        let sessionId = null;
        let meetingId = null;
        let sessionStartTime = null;
        let segmentCount = 0;
        let durationInterval = null;

        // Initialize Teams SDK
        microsoftTeams.app.initialize().then(() => {
            console.log('Teams SDK initialized');
            
            // Get meeting context
            microsoftTeams.app.getContext().then((context) => {
                meetingId = context.meeting?.id || 'demo-meeting';
                console.log('Meeting ID:', meetingId);
                initializeSignalR();
            });
        });

        // Initialize SignalR connection
        function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("/transcriptionHub")
                .build();

            // Handle connection events
            connection.start().then(() => {
                console.log('SignalR connected');
                updateStatus('Connected', 'success');
                
                // Join meeting group
                connection.invoke("JoinMeeting", meetingId);
                
            }).catch((err) => {
                console.error('SignalR connection error:', err);
                showError('Failed to connect to transcription service');
                updateStatus('Disconnected', 'error');
            });

            // Handle transcription segments
            connection.on("TranscriptionSegment", (segment) => {
                addTranscriptionSegment(segment);
            });

            // Handle transcription updates
            connection.on("TranscriptionUpdate", (session) => {
                updateSessionInfo(session);
            });

            // Handle errors
            connection.on("Error", (message) => {
                showError(message);
            });

            // Handle connection closed
            connection.onclose(() => {
                updateStatus('Disconnected', 'error');
                setTimeout(initializeSignalR, 5000); // Reconnect after 5 seconds
            });
        }

        // Start transcription
        async function startTranscription() {
            try {
                showLoading(true);

                const language = document.getElementById('languageSelect').value;

                // For demo purposes, simulate starting transcription
                // In production, this would call the actual API
                sessionId = 'demo-session-' + Date.now();
                sessionStartTime = new Date();
                isTranscribing = true;

                updateUI();
                updateStatus('Transcribing', 'active');
                clearTranscription();
                startDurationTimer();

                // Simulate receiving transcription segments
                startDemoTranscription();

            } catch (error) {
                console.error('Error starting transcription:', error);
                showError('Failed to start transcription: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // Stop transcription
        async function stopTranscription() {
            try {
                showLoading(true);

                if (sessionId) {
                    // For demo purposes, simulate stopping transcription
                    // In production, this would call the actual API
                    isTranscribing = false;
                    sessionId = null;

                    updateUI();
                    updateStatus('Stopped', 'stopped');
                    stopDurationTimer();
                    stopDemoTranscription();
                }

            } catch (error) {
                console.error('Error stopping transcription:', error);
                showError('Failed to stop transcription: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // Enhanced real-time transcription display
        let currentPartialSegment = null;
        let lastSegmentId = null;

        // Add or update transcription segment with real-time streaming
        function addTranscriptionSegment(segment) {
            const container = document.getElementById('transcriptionContainer');
            const emptyState = document.getElementById('emptyState');

            // Hide empty state
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // Handle partial vs final segments
            if (segment.isFinal) {
                addFinalSegment(segment);
            } else {
                updatePartialSegment(segment);
            }

            // Auto-scroll to bottom with smooth behavior
            smoothScrollToBottom(container);

            // Update UI state
            updateUI();
        }

        // Add final transcription segment
        function addFinalSegment(segment) {
            const container = document.getElementById('transcriptionContainer');

            // Remove any existing partial segment
            if (currentPartialSegment) {
                currentPartialSegment.remove();
                currentPartialSegment = null;
            }

            // Create final segment element
            const segmentElement = createSegmentElement(segment, false);
            segmentElement.setAttribute('data-segment-id', segment.segmentId);

            // Add to container with animation
            container.appendChild(segmentElement);

            // Trigger animation
            requestAnimationFrame(() => {
                segmentElement.style.opacity = '1';
                segmentElement.style.transform = 'translateY(0)';
            });

            // Update segment count
            segmentCount++;
            updateSegmentCount();
            lastSegmentId = segment.segmentId;
        }

        // Update partial transcription segment (real-time streaming)
        function updatePartialSegment(segment) {
            const container = document.getElementById('transcriptionContainer');

            if (currentPartialSegment) {
                // Update existing partial segment
                updateSegmentContent(currentPartialSegment, segment);
            } else {
                // Create new partial segment
                currentPartialSegment = createSegmentElement(segment, true);
                currentPartialSegment.setAttribute('data-segment-id', segment.segmentId);
                container.appendChild(currentPartialSegment);

                // Trigger animation
                requestAnimationFrame(() => {
                    currentPartialSegment.style.opacity = '1';
                    currentPartialSegment.style.transform = 'translateY(0)';
                });
            }
        }

        // Create segment element with enhanced styling
        function createSegmentElement(segment, isPartial = false) {
            const segmentElement = document.createElement('div');
            segmentElement.className = `transcription-segment ${isPartial ? 'partial' : 'final'}`;

            // Initial animation state
            segmentElement.style.opacity = '0';
            segmentElement.style.transform = 'translateY(10px)';
            segmentElement.style.transition = 'all 0.3s ease-in-out';

            updateSegmentContent(segmentElement, segment);
            return segmentElement;
        }

        // Update segment content
        function updateSegmentContent(element, segment) {
            const confidencePercentage = Math.round((segment.confidence || 0) * 100);
            const confidenceClass = getConfidenceClass(segment.confidence || 0);

            element.innerHTML = `
                <div class="segment-header">
                    <span class="speaker-name">${segment.speakerName || 'Speaker'}</span>
                    <span class="timestamp">${formatTime(new Date(segment.timestamp))}</span>
                    ${element.classList.contains('partial') ? '<span class="partial-indicator">●</span>' : ''}
                </div>
                <div class="segment-text">${segment.text}</div>
                <div class="confidence-indicator">
                    <span>Confidence:</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill ${confidenceClass}" style="width: ${confidencePercentage}%"></div>
                    </div>
                    <span>${confidencePercentage}%</span>
                </div>
            `;
        }

        // Get confidence level class for styling
        function getConfidenceClass(confidence) {
            if (confidence >= 0.8) return 'high-confidence';
            if (confidence >= 0.6) return 'medium-confidence';
            return 'low-confidence';
        }

        // Smooth scroll to bottom
        function smoothScrollToBottom(container) {
            const scrollOptions = {
                top: container.scrollHeight,
                behavior: 'smooth'
            };

            // Use smooth scrolling if supported, otherwise fallback to instant
            if ('scrollBehavior' in document.documentElement.style) {
                container.scrollTo(scrollOptions);
            } else {
                container.scrollTop = container.scrollHeight;
            }
        }

        // Update UI state
        function updateUI() {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const exportBtn = document.getElementById('exportBtn');
            const languageSelect = document.getElementById('languageSelect');

            startBtn.disabled = isTranscribing;
            stopBtn.disabled = !isTranscribing;
            languageSelect.disabled = isTranscribing;

            // Enable export button if there are transcription segments
            const hasSegments = document.querySelectorAll('.transcription-segment').length > 0;
            exportBtn.disabled = !hasSegments;
        }

        // Update status indicator
        function updateStatus(text, type) {
            const statusText = document.getElementById('statusText');
            const statusDot = document.getElementById('statusDot');
            
            statusText.textContent = text;
            
            // Update dot color based on status
            statusDot.style.backgroundColor = {
                'success': '#28a745',
                'active': '#007bff',
                'error': '#dc3545',
                'stopped': '#6c757d'
            }[type] || '#28a745';
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Show/hide loading state
        function showLoading(show) {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            
            if (show) {
                startBtn.innerHTML = '<span class="loading-spinner"></span> Starting...';
                stopBtn.innerHTML = '<span class="loading-spinner"></span> Stopping...';
            } else {
                startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
                stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
            }
        }

        // Clear transcription
        function clearTranscription() {
            const container = document.getElementById('transcriptionContainer');
            container.innerHTML = '<div class="empty-state" id="emptyState" style="display: none;"></div>';
            segmentCount = 0;
            updateSegmentCount();
        }

        // Start duration timer
        function startDurationTimer() {
            durationInterval = setInterval(updateDuration, 1000);
        }

        // Stop duration timer
        function stopDurationTimer() {
            if (durationInterval) {
                clearInterval(durationInterval);
                durationInterval = null;
            }
        }

        // Update session duration
        function updateDuration() {
            if (sessionStartTime) {
                const now = new Date();
                const duration = Math.floor((now - sessionStartTime) / 1000);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;
                
                document.getElementById('sessionDuration').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // Update segment count
        function updateSegmentCount() {
            document.getElementById('segmentCount').textContent = `${segmentCount} segments`;
        }

        // Format time
        function formatTime(date) {
            return date.toLocaleTimeString('en-US', { 
                hour12: false, 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            });
        }

        // Update session info
        function updateSessionInfo(session) {
            // Update any session-specific information
            console.log('Session updated:', session);
        }

        // Demo transcription functionality
        let demoInterval = null;
        const demoSegments = [
            { text: "Welcome everyone to today's meeting. Let's start with the agenda.", speaker: "John Smith" },
            { text: "Thank you John. I'd like to discuss the quarterly results first.", speaker: "Sarah Johnson" },
            { text: "The numbers look promising this quarter. Revenue is up 15%.", speaker: "Mike Chen" },
            { text: "That's excellent news. What about our customer satisfaction scores?", speaker: "John Smith" },
            { text: "Customer satisfaction has improved to 4.2 out of 5 stars.", speaker: "Sarah Johnson" },
            { text: "Great work team. Let's move on to the next quarter planning.", speaker: "John Smith" }
        ];
        let demoIndex = 0;

        // Enhanced demo with real-time streaming simulation
        let demoStreamInterval = null;
        let currentDemoSegment = null;
        let currentWordIndex = 0;

        function startDemoTranscription() {
            demoIndex = 0;
            simulateRealTimeTranscription();
        }

        function simulateRealTimeTranscription() {
            if (demoIndex >= demoSegments.length) {
                demoIndex = 0; // Loop back to beginning
            }

            if (!isTranscribing) return;

            const fullText = demoSegments[demoIndex].text;
            const words = fullText.split(' ');
            const speaker = demoSegments[demoIndex].speaker;
            currentWordIndex = 0;

            // Start streaming words progressively
            demoStreamInterval = setInterval(() => {
                if (!isTranscribing) {
                    clearInterval(demoStreamInterval);
                    return;
                }

                currentWordIndex++;
                const partialText = words.slice(0, currentWordIndex).join(' ');
                const isComplete = currentWordIndex >= words.length;

                const segment = {
                    segmentId: 'demo-' + demoIndex,
                    sessionId: sessionId,
                    text: partialText,
                    speakerName: speaker,
                    timestamp: new Date().toISOString(),
                    confidence: isComplete ? 0.85 + Math.random() * 0.15 : 0.6 + Math.random() * 0.2,
                    isFinal: isComplete
                };

                addTranscriptionSegment(segment);

                if (isComplete) {
                    clearInterval(demoStreamInterval);
                    demoIndex++;

                    // Start next segment after a pause
                    setTimeout(() => {
                        if (isTranscribing) {
                            simulateRealTimeTranscription();
                        }
                    }, 2000);
                }
            }, 300); // Add word every 300ms for realistic typing speed
        }

        function stopDemoTranscription() {
            if (demoInterval) {
                clearInterval(demoInterval);
                demoInterval = null;
            }
            if (demoStreamInterval) {
                clearInterval(demoStreamInterval);
                demoStreamInterval = null;
            }
        }

        // Export transcription functionality
        function exportTranscription() {
            try {
                const segments = document.querySelectorAll('.transcription-segment');
                if (segments.length === 0) {
                    showError('No transcription data to export');
                    return;
                }

                let exportText = `AccureMD Transcription Export\n`;
                exportText += `Meeting ID: ${meetingId}\n`;
                exportText += `Session ID: ${sessionId}\n`;
                exportText += `Export Date: ${new Date().toLocaleString()}\n`;
                exportText += `\n${'='.repeat(50)}\n\n`;

                segments.forEach((segment, index) => {
                    const speakerName = segment.querySelector('.speaker-name')?.textContent || 'Unknown';
                    const timestamp = segment.querySelector('.timestamp')?.textContent || '';
                    const text = segment.querySelector('.segment-text')?.textContent || '';

                    exportText += `[${timestamp}] ${speakerName}:\n${text}\n\n`;
                });

                // Create and download file
                const blob = new Blob([exportText], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `AccureMD_Transcription_${sessionId || 'export'}_${new Date().toISOString().slice(0, 10)}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                console.log('Transcription exported successfully');
            } catch (error) {
                console.error('Error exporting transcription:', error);
                showError('Failed to export transcription');
            }
        }

        // Initialize UI
        updateUI();
    </script>
</body>
</html>

<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>AccureMD-Bot-12345</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AccureMD.Core\AccureMD.Core.csproj" />
    <ProjectReference Include="..\AccureMD.Services\AccureMD.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bot.Builder" Version="4.22.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.22.2" />
    <!-- Microsoft.Bot.Builder.Teams is deprecated - Teams functionality is now built into the core Bot Builder -->
    <!-- <PackageReference Include="Microsoft.Bot.Builder.Teams" Version="4.22.2" /> -->
    <PackageReference Include="Microsoft.Bot.Connector" Version="4.22.2" />
    <PackageReference Include="Microsoft.Graph" Version="5.42.0" />
    <!-- Microsoft.Graph.Auth is deprecated and has been removed. Use Azure.Identity for authentication. -->
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="wwwroot\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>

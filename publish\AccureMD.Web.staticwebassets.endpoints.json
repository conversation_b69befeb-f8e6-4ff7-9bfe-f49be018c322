{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "static/config.html", "AssetFile": "static/config.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18559"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:32:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM="}]}, {"Route": "static/config.vgb0ih5k4i.html", "AssetFile": "static/config.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18559"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:32:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vgb0ih5k4i"}, {"Name": "integrity", "Value": "sha256-8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM="}, {"Name": "label", "Value": "static/config.html"}]}, {"Route": "static/meeting-sidebar-legacy.html", "AssetFile": "static/meeting-sidebar-legacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39469"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:31:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos="}]}, {"Route": "static/meeting-sidebar-legacy.q6lm9p6f9d.html", "AssetFile": "static/meeting-sidebar-legacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39469"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:31:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q6lm9p6f9d"}, {"Name": "integrity", "Value": "sha256-Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos="}, {"Name": "label", "Value": "static/meeting-sidebar-legacy.html"}]}, {"Route": "static/meeting-sidebar.html", "AssetFile": "static/meeting-sidebar.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:43:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}]}, {"Route": "static/meeting-sidebar.i0k7idjahb.html", "AssetFile": "static/meeting-sidebar.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:43:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i0k7idjahb"}, {"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}, {"Name": "label", "Value": "static/meeting-sidebar.html"}]}]}
{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.23/MicrosoftTeams.schema.json", "manifestVersion": "1.23", "version": "1.0.0", "id": "13a1a980-f47f-4311-bec2-150c500e2577", "packageName": "com.accuremd.teams.transcription", "developer": {"name": "AccureMD", "websiteUrl": "https://accuremd.azurewebsites.net", "privacyUrl": "https://accuremd.azurewebsites.net/privacy", "termsOfUseUrl": "https://accuremd.azurewebsites.net/terms"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "AccureMD", "full": "AccureMD Medical Intelligence"}, "description": {"short": "Real-time meeting transcription for Microsoft Teams", "full": "AccureMD provides real-time transcription capabilities for Microsoft Teams meetings with advanced speech recognition and speaker identification features."}, "accentColor": "#FFFFFF", "bots": [{"botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "scopes": ["personal", "team", "groupchat"], "supportsFiles": false, "isNotificationOnly": false, "supportsCalling": true, "supportsVideo": true}], "composeExtensions": [{"botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "commands": [{"id": "startTranscription", "context": ["compose", "commandBox"], "description": "Start meeting transcription", "title": "Start Transcription", "type": "action", "parameters": [{"name": "language", "title": "Language", "description": "Transcription language", "inputType": "choiceset", "choices": [{"title": "English (US)", "value": "en-US"}, {"title": "English (UK)", "value": "en-GB"}, {"title": "Spanish", "value": "es-ES"}, {"title": "French", "value": "fr-FR"}, {"title": "German", "value": "de-DE"}]}]}]}], "configurableTabs": [{"configurationUrl": "https://accuremd.azurewebsites.net/config", "canUpdateConfiguration": true, "scopes": ["team", "groupchat"], "context": ["channelTab", "privateChatTab", "meetingChatTab", "meetingDetailsTab", "meetingSidePanel"]}], "staticTabs": [{"entityId": "transcription", "name": "Transcription", "contentUrl": "https://accuremd.azurewebsites.net/transcription", "websiteUrl": "https://accuremd.azurewebsites.net/transcription", "scopes": ["personal"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["accuremd.azurewebsites.net"], "webApplicationInfo": {"id": "11111111-2222-3333-4444-555555555555", "resource": "https://accuremd.azurewebsites.net"}, "authorization": {"permissions": {"resourceSpecific": [{"name": "OnlineMeeting.ReadBasic.Chat", "type": "Application"}, {"name": "OnlineMeetingParticipant.Read.Chat", "type": "Application"}, {"name": "OnlineMeetingRecording.Read.Chat", "type": "Application"}, {"name": "OnlineMeetingTranscript.Read.Chat", "type": "Application"}, {"name": "ChatMessage.Read.Chat", "type": "Application"}, {"name": "TeamsAppInstallation.Read.Chat", "type": "Application"}, {"name": "ChannelMeeting.ReadBasic.Group", "type": "Application"}, {"name": "Calls.AccessMedia.All", "type": "Application"}, {"name": "Calls.JoinGroupCall.All", "type": "Application"}]}}, "meetingExtensionDefinition": {"scenes": [{"id": "transcription-scene", "name": "Transcription View", "file": "transcription-scene.json", "preview": "transcription-preview.png", "maxAudience": 50, "seatsReservedForOrganizersOrPresenters": 0}], "supportsStreaming": true}}
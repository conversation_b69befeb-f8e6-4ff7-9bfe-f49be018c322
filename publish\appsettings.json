{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Bot": "Information", "AccureMD": "Debug"}}, "AllowedHosts": "*", "Bot": {"MicrosoftAppId": "", "MicrosoftAppPassword": "", "MicrosoftAppTenantId": "", "BotName": "AccureMD", "BaseUrl": "https://your-domain.azurewebsites.net", "StateConnectionString": "", "StorageConnectionString": "", "ApplicationInsightsKey": "", "Graph": {"BaseUrl": "https://graph.microsoft.com/v1.0", "Scopes": ["https://graph.microsoft.com/OnlineMeetings.ReadWrite", "https://graph.microsoft.com/Calls.AccessMedia.All", "https://graph.microsoft.com/Calls.JoinGroupCall.All"], "TimeoutSeconds": 30}, "Transcription": {"DefaultLanguage": "en-US", "EnableSpeakerDiarization": true, "ConfidenceThreshold": 0.7, "MaxSpeakers": 10, "Voxtral": {"ApiEndpoint": "", "ApiKey": "", "ModelVersion": "latest", "ModelParameters": {}}}, "Recording": {"AudioFormat": "WAV", "VideoFormat": "MP4", "AudioSampleRate": 44100, "AudioBitrate": 128000, "VideoResolution": "1920x1080", "VideoFrameRate": 30, "VideoBitrate": 2000000, "StorageProvider": "AzureBlob", "MaxDurationMinutes": 480}}, "Database": {"ConnectionString": "", "Provider": "SqlServer"}, "Azure": {"Storage": {"ConnectionString": "", "ContainerName": "accuremd-recordings"}, "ApplicationInsights": {"InstrumentationKey": ""}}, "ExternalServices": {"SummarizationApi": {"BaseUrl": "", "ApiKey": "", "TimeoutSeconds": 60}, "VoxtralApi": {"BaseUrl": "", "ApiKey": "", "ModelEndpoint": "", "TimeoutSeconds": 30}}}
const fs = require('fs');
const path = require('path');
const Ajv = require('ajv');

/**
 * Validates the Teams app manifest against the schema
 */
async function validateManifest() {
    console.log('Validating Teams app manifest...');
    
    try {
        // Read the manifest file
        const manifestPath = path.join(__dirname, 'manifest.json');
        const manifestContent = fs.readFileSync(manifestPath, 'utf8');
        const manifest = JSON.parse(manifestContent);
        
        // Basic validation checks
        const errors = [];
        
        // Check required fields
        const requiredFields = ['manifestVersion', 'version', 'id', 'name', 'description', 'developer'];
        for (const field of requiredFields) {
            if (!manifest[field]) {
                errors.push(`Missing required field: ${field}`);
            }
        }
        
        // Check version format
        if (manifest.version && !/^\d+\.\d+\.\d+$/.test(manifest.version)) {
            errors.push('Version must be in format x.y.z');
        }
        
        // Check manifest version
        const supportedVersions = ['1.16', '1.17', '1.19', '1.20', '1.21', '1.22', '1.23'];
        if (!supportedVersions.includes(manifest.manifestVersion)) {
            errors.push(`Manifest version should be one of: ${supportedVersions.join(', ')}`);
        }
        
        // Check bot configuration
        if (manifest.bots && manifest.bots.length > 0) {
            const bot = manifest.bots[0];
            if (!bot.botId || bot.botId.includes('{{')) {
                errors.push('Bot ID must be configured (replace {{BOT_ID}} placeholder)');
            }
        }
        
        // Check domain configuration
        if (manifest.validDomains && manifest.validDomains.some(domain => domain.includes('{{'))) {
            errors.push('Valid domains must be configured (replace {{DOMAIN_NAME}} placeholders)');
        }
        
        // Check required image files
        const imageFiles = ['color.png', 'outline.png'];
        for (const imageFile of imageFiles) {
            const imagePath = path.join(__dirname, imageFile);
            if (!fs.existsSync(imagePath)) {
                errors.push(`Missing required image file: ${imageFile}`);
            }
        }
        
        if (errors.length > 0) {
            console.error('Manifest validation failed:');
            errors.forEach(error => console.error(`  - ${error}`));
            process.exit(1);
        } else {
            console.log('Manifest validation passed!');
        }
        
    } catch (error) {
        console.error('Error validating manifest:', error.message);
        process.exit(1);
    }
}

// Run validation if this script is executed directly
if (require.main === module) {
    validateManifest().catch(console.error);
}

module.exports = { validateManifest };

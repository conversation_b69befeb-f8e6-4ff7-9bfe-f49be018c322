using AccureMD.Bot.Bots;
using AccureMD.Bot.Configuration;
using AccureMD.Bot.Middleware;
using AccureMD.Core.Interfaces;
using AccureMD.Services.Implementation;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Connector.Authentication;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

var builder = WebApplication.CreateBuilder(args);

// Add configuration
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddUserSecrets<Program>();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Configure Bot Framework
var botConfig = new BotConfiguration();
builder.Configuration.GetSection("Bot").Bind(botConfig);
builder.Services.AddSingleton(botConfig);

// Bot Framework Authentication
builder.Services.AddSingleton<BotFrameworkAuthentication, ConfigurationBotFrameworkAuthentication>();

// Bot Adapter
builder.Services.AddSingleton<IBotFrameworkHttpAdapter, CloudAdapter>();

// Bot implementation
builder.Services.AddTransient<IBot, AccureMDBot>();


// AccureMD Services
builder.Services.AddScoped<IMeetingService, MeetingService>();
builder.Services.AddScoped<ITranscriptionService, TranscriptionService>();
builder.Services.AddScoped<IRecordingService, RecordingService>();

// HTTP Client for external services
builder.Services.AddHttpClient();

// SignalR for real-time communication
builder.Services.AddSignalR();

// CORS configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowTeams", policy =>
    {
        policy.WithOrigins("https://teams.microsoft.com", "https://*.teams.microsoft.com")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseSecurityHeaders();
app.UseErrorHandling();
app.UseRequestLogging();
app.UseCors("AllowTeams");
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Bot Framework endpoint
app.MapPost("/api/messages", async (IBotFrameworkHttpAdapter adapter, IBot bot, HttpContext context) =>
{
    await adapter.ProcessAsync(context.Request, context.Response, bot);
});

// SignalR hub for real-time transcription
app.MapHub<AccureMD.Bot.Hubs.TranscriptionHub>("/transcriptionHub");

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { status = "healthy", timestamp = DateTime.UtcNow }));

app.Run();
